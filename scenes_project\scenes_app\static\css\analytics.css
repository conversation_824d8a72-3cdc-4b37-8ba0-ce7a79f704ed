/* Enhanced Analytics Dashboard Styles */

/* Mobile-first responsive design */
.analytics-dashboard {
    min-height: 100vh;
}

/* Enhanced card animations */
.analytics-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
}

.analytics-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #6366f1, transparent);
    transition: left 0.5s;
}

.analytics-card:hover::before {
    left: 100%;
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: #d1d5db;
}

/* Stat number animations */
.stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.analytics-card:hover .stat-number {
    transform: scale(1.05);
}

/* Chart container enhancements */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
    padding: 10px;
}

.chart-container canvas {
    border-radius: 8px;
}

/* Filter section styling */
.analytics-filters {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    box-shadow: 0 4px 6px -1px rgba(255, 255, 255, 0.1);
}

.analytics-filters select {
    transition: all 0.2s ease;
}

.analytics-filters select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

/* Comparison section styling */
.analytics-comparison {
    background: linear-gradient(135deg, #fefefe 0%, #f9fafb 100%);
    border: 1px solid #e5e7eb;
}

.comparison-snapshot {
    transition: all 0.3s ease;
    cursor: pointer;
}

.comparison-snapshot:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Insights section styling */
.analytics-insights {
    background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
    border: 1px solid #e5e7eb;
}

.insight-item {
    transition: all 0.3s ease;
    animation: slideInUp 0.5s ease-out;
}

.insight-item:hover {
    transform: translateX(5px);
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }

    100% {
        background-position: -200% 0;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Chart type buttons */
.chart-type-btn {
    padding: 8px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 6px;
    color: #6b7280;
    transition: all 0.2s;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.chart-type-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.chart-type-btn:hover::before {
    left: 100%;
}

.chart-type-btn:hover {
    background: #f3f4f6;
    color: #374151;
    transform: translateY(-1px);
}

.chart-type-btn.active {
    background: #6366f1;
    color: white;
    border-color: #6366f1;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Real-time controls */
.real-time-controls {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px;
}

/* Data tables */
.data-table {
    max-height: 200px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.data-table::-webkit-scrollbar {
    width: 4px;
}

.data-table::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
}

.data-table::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.data-table::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Most favorited scenes */
.most-favorited-item {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.most-favorited-item:hover {
    border-left-color: #6366f1;
    background: linear-gradient(90deg, rgba(99, 102, 241, 0.05), transparent);
}

/* Responsive design */
@media (max-width: 768px) {
    .analytics-card {
        margin-bottom: 1rem;
    }

    .chart-container {
        height: 250px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .analytics-filters .grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .comparison-snapshot {
        margin-bottom: 1rem;
    }

    .real-time-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 640px) {
    .analytics-dashboard {
        padding: 1rem;
    }

    .analytics-card {
        padding: 1rem;
    }

    .chart-container {
        height: 200px;
        padding: 5px;
    }

    .stat-number {
        font-size: 1.25rem;
    }
}

/* Print styles */
@media print {
    .analytics-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
        margin-bottom: 1rem;
    }

    .chart-container {
        height: 200px;
    }

    .no-print {
        display: none !important;
    }

    .analytics-filters,
    .analytics-comparison,
    .real-time-controls {
        display: none !important;
    }
}

/* Accessibility improvements */
.analytics-card:focus-within {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
}

.chart-type-btn:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .analytics-card {
        border: 2px solid #000;
    }

    .stat-number {
        color: #000;
        -webkit-text-fill-color: #000;
    }

    .chart-type-btn.active {
        background: #000;
        color: #fff;
        border-color: #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {

    .analytics-card,
    .chart-type-btn,
    .comparison-snapshot,
    .insight-item {
        transition: none;
        animation: none;
    }

    .analytics-card::before {
        display: none;
    }
}

/* Custom scrollbar for webkit browsers */
.analytics-dashboard::-webkit-scrollbar {
    width: 8px;
}

.analytics-dashboard::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.analytics-dashboard::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.analytics-dashboard::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Tooltip styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #1f2937;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Success/Error states */
.success-state {
    border-left: 4px solid #10b981;
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.1), transparent);
}

.error-state {
    border-left: 4px solid #ef4444;
    background: linear-gradient(90deg, rgba(239, 68, 68, 0.1), transparent);
}

.warning-state {
    border-left: 4px solid #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.1), transparent);
}

#comparison-scenes-chart,
#comparison-favorites-chart {
  max-width: 100% !important;
  max-height: 300px !important;
  width: 100% !important;
  height: 300px !important;
}