<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
  <title>Whisphers of Intemecy</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#6366f1',
            'primary-light': '#a5b4fc',
            'primary-dark': '#4f46e5',
            accent: '#f59e0b',
            'accent-light': '#fbbf24',
            neutral: '#64748b'
          },
          fontFamily: { sans: ['Inter', 'ui-sans-serif', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial'] }
        }
      }
    }
  </script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css" />
  {% load static %}
  <link rel="stylesheet" href="{% static 'css/styles.css' %}">
  <link rel="stylesheet" href="{% static 'css/mobile-responsive.css' %}">
  <link rel="stylesheet" href="{% static 'css/pagination.css' %}">
  <link rel="stylesheet" href="{% static 'css/hero-carousel.css' %}">

</head>

<body class="min-h-screen bg-gray-50 text-slate-900 font-sans antialiased">
  <header class="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-20 shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-4 sm:py-5">
        <!-- Logo/Brand -->
        <a href="/" class="group flex items-center space-x-2 sm:space-x-3">
          <div>
            <div
              class="text-lg sm:text-xl font-light text-gray-900 tracking-tight group-hover:text-primary transition-colors">
              <span class="font-semibold">Whisphers</span> of <span class="text-accent font-medium">Intimacy</span>
            </div>
            <div class="text-xs text-gray-500 -mt-1 hidden sm:block">Romantic Scene Collection</div>
          </div>
        </a>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-4 lg:space-x-6">
          <!-- Home button -->
          <a href="{% url 'scene_list' %}"
            class="inline-flex items-center px-3 lg:px-4 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-primary hover:bg-gray-50 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M11.47 3.84a.75.75 0 011.06 0l8.69 8.69a.75.75 0 101.06-1.06l-8.689-8.69a2.25 2.25 0 00-3.182 0l-8.69 8.69a.75.75 0 001.061 1.06l8.69-8.69z" />
              <path
                d="M12 5.432l8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 01-.75-.75v-4.5a.75.75 0 00-.75-.75h-3a.75.75 0 00-.75.75V21a.75.75 0 01-.75.75H5.625a1.875 1.875 0 01-1.875-1.875v-6.198a2.29 2.29 0 00.091-.086L12 5.43z" />
            </svg>
            <span class="hidden lg:inline">Home</span>
          </a>
          <!-- Search Bar -->
          <div class="relative">
            <form method="GET" action="{% url 'search_results' %}" class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
              <input
                type="text"
                name="q"
                placeholder="Search scenes..."
                class="nav-search-input block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-sm bg-white"
                id="nav-search-query"
                autocomplete="off"
                style="width: 200px;"
              >
              <!-- Autocomplete dropdown -->
              <div id="nav-autocomplete-dropdown" class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg hidden max-h-60 overflow-y-auto">
                <!-- Suggestions will be populated here -->
              </div>
            </form>
          </div>
          <!-- Analytics -->
          <a href="{% url 'analytics' %}"
            class="inline-flex items-center px-3 lg:px-4 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-primary hover:bg-gray-50 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M4 8a1 1 0 011-1h14a1 1 0 011 1v1a1 1 0 01-1 1H5a1 1 0 01-1-1V8zm3 4a1 1 0 011-1h8a1 1 0 011 1v6a1 1 0 01-1 1H8a1 1 0 01-1-1v-6zm8 0a1 1 0 00-1 1v4a1 1 0 001 1h2a1 1 0 001-1v-4a1 1 0 00-1-1h-2z" />
            </svg>
            <span class="hidden lg:inline">Analytics</span>
          </a>
          <!-- Favorites button -->
          <a href="{% url 'favorites_list' %}"
            class="inline-flex items-center px-3 lg:px-4 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-primary hover:bg-gray-50 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                clip-rule="evenodd"></path>
            </svg>
            <span class="hidden lg:inline">Favorites</span>
            <span id="nav-favorite-count"
              class="ml-1 text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full hidden"></span>
          </a>

          <!-- Copy Random Scene button -->
          <button id="copy-random-scene-btn"
            class="inline-flex items-center px-3 lg:px-4 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-primary hover:bg-gray-50 transition-all duration-200"
            title="Copy random scene to clipboard">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
              </path>
            </svg>
            <span class="hidden lg:inline">Copy Random</span>
          </button>

          <!-- Add scene button-->
          <a href="{% url 'add_scene' %}"
            class="inline-flex items-center px-3 lg:px-4 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-primary hover:bg-gray-50 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z">
              </path>
            </svg>
            <span class="hidden lg:inline">Add Scene</span>
          </a>
        </nav>

        <!-- Mobile Menu Toggle Button -->
        <button onclick="toggleMobileMenu()"
          class="md:hidden bg-gray-100 hover:bg-gray-200 p-3 rounded-lg flex items-center justify-center w-12 h-12">
          <!-- Hamburger Icon -->
          <svg id="menu-bars" class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" stroke-width="2"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <!-- Close Icon -->
          <svg id="menu-x" class="w-5 h-5 text-gray-500 hidden" fill="none" stroke="currentColor" stroke-width="2"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Mobile Navigation Menu -->
      <div id="mobile-menu" class="md:hidden border-t border-gray-200 bg-white shadow-lg rounded-b-2xl hidden">
        <nav class="flex flex-col divide-y divide-gray-100">

          <a href="{% url 'scene_list' %}"
            class="flex items-center gap-3 px-5 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
            <svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3 9.75L12 3l9 6.75V21a1 1 0 01-1 1h-5a1 1 0 01-1-1v-6H10v6a1 1 0 01-1 1H4a1 1 0 01-1-1V9.75z" />
            </svg>
            <span class="text-base font-medium">Home</span>
          </a>

          <!-- Mobile Search Bar -->
          <div class="px-5 py-3">
            <form method="GET" action="{% url 'search_results' %}" class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
              <input
                type="text"
                name="q"
                placeholder="Search scenes..."
                class="mobile-search-input block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-sm bg-white"
                id="mobile-search-query"
                autocomplete="off"
              >
              <!-- Mobile Autocomplete dropdown -->
              <div id="mobile-autocomplete-dropdown" class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg hidden max-h-60 overflow-y-auto">
                <!-- Suggestions will be populated here -->
              </div>
            </form>
          </div>

          <a href="{% url 'analytics' %}"
            class="flex items-center gap-3 px-5 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
            <svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M4 8a1 1 0 011-1h14a1 1 0 011 1v1a1 1 0 01-1 1H5a1 1 0 01-1-1V8zm3 4a1 1 0 011-1h8a1 1 0 011 1v6a1 1 0 01-1 1H8a1 1 0 01-1-1v-6zm8 0a1 1 0 00-1 1v4a1 1 0 001 1h2a1 1 0 001-1v-4a1 1 0 00-1-1h-2z" />
            </svg>
            <span class="text-base font-medium">Analytics</span>
          </a>

          <a href="{% url 'favorites_list' %}"
            class="flex items-center gap-3 px-5 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
            <svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                clip-rule="evenodd"></path>
            </svg>
            <span class="text-base font-medium">Favorites</span>
          </a>

          <a href="#" id="mobile-copy-random-scene-btn"
            class="flex items-center gap-3 px-5 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
            <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
              </path>
            </svg>
            <span class="text-base font-medium">Copy Random</span>
          </a>

          <a href="{% url 'add_scene' %}"
            class="flex items-center gap-3 px-5 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
            <svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z">
              </path>
            </svg>
            <span class="text-base font-medium">Add Scene</span>
          </a>

          <a href="{% url 'random_scene' %}"
            class="flex items-center gap-3 px-5 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">
            <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
              </path>
            </svg>
            <span class="text-base font-medium">Random Scene</span>
          </a>

        </nav>
      </div>
    </div>
  </header>

  <main id="main" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
    {% block content %}{% endblock %}
  </main>

  <!-- Simple Footer -->
  <footer class="bg-gradient-to-r from-gray-50 to-white border-t border-gray-200 mt-12 sm:mt-16 lg:mt-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
      <div class="text-center">
        <div class="flex items-center justify-center mb-4">
          <div class="text-base sm:text-lg font-light text-gray-900">
            <span class="font-semibold">Whisphers</span> of <span class="text-accent font-medium">Intimacy</span>
          </div>
        </div>
        <p class="text-sm text-gray-600 mb-4">Curating beautiful romantic scenes from around the world</p>
        <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-6 text-xs text-gray-500">
          <span>© {% now 'Y' %} All rights reserved</span>
          <span class="hidden sm:inline">•</span>
          <span>Made with ❤️ for storytellers</span>
        </div>
      </div>
  </footer>

  </footer>

  <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  {% csrf_token %}
  <script>
    // Make CSRF token available globally
    window.csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    // Make total favorites count available globally
    window.totalFavorites = {{ total_favorites |default:0 }};

    // Simple mobile menu function (like web app)
    function toggleMobileMenu() {
      const menu = document.getElementById('mobile-menu');
      const bars = document.getElementById('menu-bars');
      const x = document.getElementById('menu-x');

      if (menu.style.display === 'none' || menu.style.display === '') {
        // Open menu
        menu.style.display = 'block';
        bars.style.display = 'none';
        x.style.display = 'block';
        document.body.style.overflow = 'hidden';
      } else {
        // Close menu
        menu.style.display = 'none';
        bars.style.display = 'block';
        x.style.display = 'none';
        document.body.style.overflow = '';
      }
    }

    // Close menu when clicking on links (like web app)
    document.addEventListener('DOMContentLoaded', function () {
      const menuLinks = document.querySelectorAll('#mobile-menu a');
      menuLinks.forEach(link => {
        link.addEventListener('click', function () {
          toggleMobileMenu();
        });
      });

      // Close menu on window resize
      window.addEventListener('resize', function () {
        const menu = document.getElementById('mobile-menu');
        if (window.innerWidth >= 768 && menu.style.display === 'block') {
          toggleMobileMenu();
        }
      });

    });
  </script>
  <script src="{% static 'js/main.js' %}"></script>
  <script src="{% static 'js/scene-management.js' %}"></script>
  <script src="{% static 'js/advanced-pagination.js' %}"></script>
  <script src="{% static 'js/nav-search.js' %}"></script>
  <script src="{% static 'js/hero-carousel.js' %}"></script>

</body>

</html>