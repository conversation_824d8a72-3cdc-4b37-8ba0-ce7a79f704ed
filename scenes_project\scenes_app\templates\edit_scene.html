{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
  <!-- Mobile-Optimized Header -->
  <div class="mb-6 sm:mb-8">
    <!-- Mobile Navigation -->
    <nav class="flex items-center space-x-2 sm:space-x-4 mb-4 sm:mb-6">
      <a href="{% url 'scene_detail' scene.id %}" class="group inline-flex items-center px-3 sm:px-4 py-2 sm:py-3 rounded-lg sm:rounded-xl bg-gray-50 text-gray-600 font-medium hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-all duration-200 text-sm sm:text-base" style="min-height: 44px;">
        <svg class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="hidden sm:inline">Back to Scene</span>
        <span class="sm:hidden">Back</span>
      </a>
      <div class="text-gray-300 hidden sm:block">/</div>
      <span class="text-xs sm:text-sm text-gray-500 font-medium">Edit Scene #{{ scene.id }}</span>
    </nav>

    <!-- Mobile Action Buttons -->
    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
      <button id="save-scene" class="inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-lg sm:rounded-xl bg-gradient-to-r from-green-600 to-green-700 text-white font-medium hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base order-1" style="min-height: 44px;">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        Save Changes
      </button>
      
      <a href="{% url 'delete_scene' scene.id %}" class="inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-lg sm:rounded-xl bg-gradient-to-r from-red-600 to-red-700 text-white font-medium hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base order-2" style="min-height: 44px;">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
        <span class="hidden sm:inline">Delete Scene</span>
        <span class="sm:hidden">Delete</span>
      </a>
    </div>
  </div>

  <!-- Mobile-Optimized Edit Form -->
  <form id="edit-scene-form" class="space-y-6 sm:space-y-8">
    {% csrf_token %}
    
    <!-- Basic Information -->
    <div class="bg-white rounded-xl sm:rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="px-4 sm:px-8 py-4 sm:py-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
        <h2 class="text-lg sm:text-2xl font-light text-gray-900 mb-1 sm:mb-2">Basic Information</h2>
        <p class="text-sm sm:text-base text-gray-600">Core details about the scene</p>
      </div>
      
      <div class="px-4 sm:px-8 py-6 sm:py-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
          <div class="space-y-4 sm:space-y-6">
            <div>
              <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Scene Title</label>
              <input type="text" id="title" name="title" value="{{ scene.title }}" 
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                     placeholder="Enter scene title" required style="font-size: 16px; min-height: 44px;">
            </div>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label for="effeminate_age" class="block text-sm font-medium text-gray-700 mb-2">Effeminate Age</label>
                <input type="number" id="effeminate_age" name="effeminate_age" value="{{ scene.effeminate_age }}" 
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                       min="18" max="100" required style="font-size: 16px; min-height: 44px;">
              </div>
              <div>
                <label for="masculine_age" class="block text-sm font-medium text-gray-700 mb-2">Masculine Age</label>
                <input type="number" id="masculine_age" name="masculine_age" value="{{ scene.masculine_age }}" 
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                       min="18" max="100" required style="font-size: 16px; min-height: 44px;">
              </div>
            </div>
          </div>
          
          <div class="space-y-4 sm:space-y-6">
            <div>
              <label for="country" class="block text-sm font-medium text-gray-700 mb-2">Country</label>
              <input type="text" id="country" name="country" value="{{ scene.country }}" 
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                     placeholder="Enter country" required style="font-size: 16px; min-height: 44px;">
            </div>
            
            <div>
              <label for="setting" class="block text-sm font-medium text-gray-700 mb-2">Setting</label>
              <input type="text" id="setting" name="setting" value="{{ scene.setting }}" 
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                     placeholder="Enter setting" required style="font-size: 16px; min-height: 44px;">
            </div>
            
            <div>
              <label for="emotion" class="block text-sm font-medium text-gray-700 mb-2">Emotion</label>
              <input type="text" id="emotion" name="emotion" value="{{ scene.emotion }}" 
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                     placeholder="Enter emotion" required style="font-size: 16px; min-height: 44px;">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Character Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
      <!-- Effeminate Character -->
      <div class="bg-white rounded-xl sm:rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-4 sm:px-8 py-4 sm:py-6 bg-gradient-to-r from-pink-50 to-rose-50 border-b border-gray-100">
          <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-1 sm:mb-2">Effeminate Character</h3>
          <p class="text-sm sm:text-base text-gray-600">Character appearance and details</p>
        </div>
        
        <div class="px-4 sm:px-8 py-6 sm:py-8 space-y-4 sm:space-y-6">
          <div>
            <label for="effeminate_appearance" class="block text-sm font-medium text-gray-700 mb-2">Appearance</label>
            <textarea id="effeminate_appearance" name="effeminate_appearance" rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 text-base"
                      placeholder="Describe appearance" style="font-size: 16px;">{{ scene.details.effeminate.appearance|default:'' }}</textarea>
          </div>
          
          <div>
            <label for="effeminate_hair" class="block text-sm font-medium text-gray-700 mb-2">Hair Style</label>
            <textarea id="effeminate_hair" name="effeminate_hair" rows="2"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 text-base"
                      placeholder="Describe hair style" style="font-size: 16px;">{{ scene.details.effeminate.hair|default:'' }}</textarea>
          </div>
          
          <div>
            <label for="effeminate_clothing" class="block text-sm font-medium text-gray-700 mb-2">Clothing</label>
            <textarea id="effeminate_clothing" name="effeminate_clothing" rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200 text-base"
                      placeholder="Describe clothing" style="font-size: 16px;">{{ scene.details.effeminate.clothing|default:'' }}</textarea>
          </div>
        </div>
      </div>

      <!-- Masculine Character -->
      <div class="bg-white rounded-xl sm:rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-4 sm:px-8 py-4 sm:py-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
          <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-1 sm:mb-2">Masculine Character</h3>
          <p class="text-sm sm:text-base text-gray-600">Character appearance and details</p>
        </div>
        
        <div class="px-4 sm:px-8 py-6 sm:py-8 space-y-4 sm:space-y-6">
          <div>
            <label for="masculine_appearance" class="block text-sm font-medium text-gray-700 mb-2">Appearance</label>
            <textarea id="masculine_appearance" name="masculine_appearance" rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                      placeholder="Describe appearance" style="font-size: 16px;">{{ scene.details.masculine.appearance|default:'' }}</textarea>
          </div>
          
          <div>
            <label for="masculine_hair" class="block text-sm font-medium text-gray-700 mb-2">Hair Style</label>
            <textarea id="masculine_hair" name="masculine_hair" rows="2"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                      placeholder="Describe hair style" style="font-size: 16px;">{{ scene.details.masculine.hair|default:'' }}</textarea>
          </div>
          
          <div>
            <label for="masculine_clothing" class="block text-sm font-medium text-gray-700 mb-2">Clothing</label>
            <textarea id="masculine_clothing" name="masculine_clothing" rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-base"
                      placeholder="Describe clothing" style="font-size: 16px;">{{ scene.details.masculine.clothing|default:'' }}</textarea>
          </div>
        </div>
      </div>
    </div>

    <!-- Atmosphere -->
    <div class="bg-white rounded-xl sm:rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="px-4 sm:px-8 py-4 sm:py-6 bg-gradient-to-r from-amber-50 to-orange-50 border-b border-gray-100">
        <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-1 sm:mb-2">Scene Atmosphere</h3>
        <p class="text-sm sm:text-base text-gray-600">Environmental details and ambiance</p>
      </div>
      
      <div class="px-4 sm:px-8 py-6 sm:py-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
          <div>
            <label for="atmosphere_lighting" class="block text-sm font-medium text-gray-700 mb-2">Lighting</label>
            <textarea id="atmosphere_lighting" name="atmosphere_lighting" rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-200 text-base"
                      placeholder="Describe lighting" style="font-size: 16px;">{{ scene.details.atmosphere.lighting|default:'' }}</textarea>
          </div>
          
          <div>
            <label for="atmosphere_scent" class="block text-sm font-medium text-gray-700 mb-2">Scent</label>
            <textarea id="atmosphere_scent" name="atmosphere_scent" rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-200 text-base"
                      placeholder="Describe scent" style="font-size: 16px;">{{ scene.details.atmosphere.scent|default:'' }}</textarea>
          </div>
          
          <div>
            <label for="atmosphere_sound" class="block text-sm font-medium text-gray-700 mb-2">Sound</label>
            <textarea id="atmosphere_sound" name="atmosphere_sound" rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-200 text-base"
                      placeholder="Describe sound" style="font-size: 16px;">{{ scene.details.atmosphere.sound|default:'' }}</textarea>
          </div>
        </div>
      </div>
    </div>

    <!-- Full Text -->
    <div class="bg-white rounded-xl sm:rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="px-4 sm:px-8 py-4 sm:py-6 bg-gradient-to-r from-purple-50 to-indigo-50 border-b border-gray-100">
        <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-1 sm:mb-2">Complete Scene Description</h3>
        <p class="text-sm sm:text-base text-gray-600">The full narrative prompt for this scene</p>
      </div>
      
      <div class="px-4 sm:px-8 py-6 sm:py-8">
        <textarea id="full_text" name="full_text" rows="8"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 font-serif text-base"
                  placeholder="Enter the complete scene description" required style="font-size: 16px;">{{ scene.full_text }}</textarea>
        <div class="mt-2 text-sm text-gray-500">
          <span id="char-count">{{ scene.full_text|length }}</span> characters
        </div>
      </div>
    </div>
  </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('edit-scene-form');
  const saveBtn = document.getElementById('save-scene');
  const fullTextArea = document.getElementById('full_text');
  const charCount = document.getElementById('char-count');
  
  // Character counter
  fullTextArea.addEventListener('input', function() {
    charCount.textContent = this.value.length;
  });
  
  // Save scene
  saveBtn.addEventListener('click', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(form);
    
    try {
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<svg class="w-5 h-5 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Saving...';
      
      const response = await fetch('', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
      });
      
      const result = await response.json();
      
      if (result.status === 'success') {
        // Show success message
        if (typeof Toastify !== 'undefined') {
          Toastify({
            text: result.message,
            duration: 3000,
            gravity: "top",
            position: "right",
            backgroundColor: "#10b981",
          }).showToast();
        }
        
        // Redirect to scene detail after a short delay
        setTimeout(() => {
          window.location.href = '{% url "scene_detail" scene.id %}';
        }, 1500);
      } else {
        throw new Error(result.message);
      }
      
    } catch (error) {
      console.error('Error saving scene:', error);
      
      if (typeof Toastify !== 'undefined') {
        Toastify({
          text: 'Error saving scene: ' + error.message,
          duration: 5000,
          gravity: "top",
          position: "right",
          backgroundColor: "#ef4444",
        }).showToast();
      } else {
        alert('Error saving scene: ' + error.message);
      }
    } finally {
      saveBtn.disabled = false;
      saveBtn.innerHTML = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Save Changes';
    }
  });
});
</script>
{% endblock %}