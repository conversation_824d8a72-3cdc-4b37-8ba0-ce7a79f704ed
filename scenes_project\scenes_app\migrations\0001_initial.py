# Generated by Django 5.2.4 on 2025-08-10 16:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Scene',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, unique=True)),
                ('effeminate_age', models.IntegerField()),
                ('masculine_age', models.IntegerField()),
                ('country', models.CharField(max_length=100)),
                ('setting', models.CharField(max_length=100)),
                ('emotion', models.CharField(max_length=100)),
                ('details', models.J<PERSON>NField(default=dict)),
                ('full_text', models.TextField()),
            ],
            options={
                'ordering': ['id'],
                'indexes': [models.Index(fields=['country'], name='scenes_app__country_852f35_idx'), models.Index(fields=['setting'], name='scenes_app__setting_5eef23_idx'), models.Index(fields=['emotion'], name='scenes_app__emotion_b42cf0_idx')],
            },
        ),
        migrations.CreateModel(
            name='FavoriteScene',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(max_length=40)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('scene', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to='scenes_app.scene')),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('scene', 'session_key')},
            },
        ),
    ]
