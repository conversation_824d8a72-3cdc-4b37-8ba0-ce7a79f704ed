{% extends 'base.html' %}
{% load static %}
{% block content %}
<!-- Navigation Header -->
<div class="mb-4 sm:mb-6 lg:mb-8 flex flex-col gap-3 sm:gap-4 mobile-mb-4 mobile-section-spacing">
  <nav class="flex items-center space-x-2 sm:space-x-4 mobile-gap-2">
    <a href="/"
      class="group inline-flex items-center px-3 sm:px-4 py-2 rounded-lg sm:rounded-xl bg-gray-50 text-gray-600 font-medium hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-all duration-200 text-sm touch-target mobile-btn mobile-px-3 mobile-py-2 mobile-text-sm">
      <svg class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform flex-shrink-0" fill="currentColor"
        viewBox="0 0 20 20">
        <path fill-rule="evenodd"
          d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
          clip-rule="evenodd"></path>
      </svg>
      <span class="hidden sm:inline mobile-hide">All Scenes</span>
      <span class="sm:hidden mobile-show">Back</span>
    </a>
    <div class="text-gray-300 hidden sm:block mobile-hide">/</div>
    <span class="text-sm text-gray-500 font-medium mobile-text-xs">Scene #{{ scene.id }}</span>
  </nav>

  <div class="mobile-btn-group flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3 mobile-gap-3">
    <!-- Favorite Button -->
    <button id="favorite-btn" data-scene-id="{{ scene.id }}" class="mobile-btn group inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-lg sm:rounded-xl font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl touch-target mobile-px-4 mobile-py-3
                     {% if is_favorited %}
                       bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-500
                     {% else %}
                       bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 hover:from-gray-200 hover:to-gray-300 focus:ring-gray-400
                     {% endif %}">
      <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-200 flex-shrink-0"
        fill="{% if is_favorited %}currentColor{% else %}none{% endif %}" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
        </path>
      </svg>
      <span id="favorite-text" class="text-sm sm:text-base mobile-text-sm truncate">
        {% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}
      </span>
    </button>

    <!-- Edit Button -->
    <a href="{% url 'edit_scene' scene.id %}"
      class="mobile-btn group inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base touch-target mobile-px-4 mobile-py-3 mobile-text-sm">
      <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-200 flex-shrink-0"
        fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
        </path>
      </svg>
      <span class="truncate">Edit Scene</span>
    </a>

    <!-- Random Button -->
    <a href="{% url 'random_scene' %}"
      class="mobile-btn group inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-lg sm:rounded-xl bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base touch-target mobile-px-4 mobile-py-3 mobile-text-sm">
      <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none"
        stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
        </path>
      </svg>
      Random Scene
      <span class="ml-2 text-xs opacity-75 hidden sm:inline">(Press R)</span>
    </a>
  </div>
</div>

<!-- Main Content Card -->
<article class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
  <!-- Hero Header -->
  <header class="relative px-4 sm:px-8 py-6 sm:py-8 bg-gradient-to-br from-gray-50 to-white border-b border-gray-100">
    <div class="absolute top-4 right-4 text-xs text-gray-400 font-mono bg-white px-2 py-1 rounded-md border">
      ID: {{ scene.id }}
    </div>

    <div class="max-w-4xl">
      <h1 class="text-2xl sm:text-3xl lg:text-4xl font-light text-gray-900 mb-4 sm:mb-6 leading-tight">
        {{ scene.title }}
      </h1>

      <!-- Enhanced Metadata -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-4 sm:mb-6">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
            <svg class="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="min-w-0">
            <div class="text-sm text-gray-500">Location</div>
            <div class="font-semibold text-gray-900 truncate">{{ scene.country }}</div>
          </div>
        </div>

        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
            <svg class="w-4 h-4 sm:w-5 sm:h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="min-w-0">
            <div class="text-sm text-gray-500">Setting</div>
            <div class="font-semibold text-gray-900 truncate">{{ scene.setting }}</div>
          </div>
        </div>

        <div class="flex items-center space-x-3 sm:col-span-2 lg:col-span-1">
          <div
            class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
            <svg class="w-4 h-4 sm:w-5 sm:h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="min-w-0">
            <div class="text-sm text-gray-500">Emotion</div>
            <div class="font-semibold text-gray-900 truncate">{{ scene.emotion }}</div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Character & Atmosphere Details -->
  <section class="px-4 sm:px-8 py-6 sm:py-8 border-b border-gray-100">
    <div class="mb-6 sm:mb-8">
      <h2 class="text-xl sm:text-2xl font-light text-gray-900 mb-2">Character Profiles</h2>
      <p class="text-gray-600 text-sm sm:text-base">Detailed descriptions of the characters and their environment</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-6 sm:mb-8">
      <!-- Effeminate Character -->
      <div class="bg-gradient-to-br from-pink-50 via-rose-50 to-pink-50 rounded-2xl p-6 sm:p-8 border border-pink-100">
        <div class="flex items-center mb-4 sm:mb-6">
          <div
            class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-pink-400 to-rose-400 flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
            <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd">
              </path>
            </svg>
          </div>
          <div class="min-w-0">
            <h3 class="text-lg sm:text-xl font-semibold text-pink-900">Effeminate Character</h3>
            <div class="text-sm text-pink-700 font-medium">Age {{ scene.effeminate_age }}</div>
          </div>
        </div>

        <div class="space-y-3 sm:space-y-4">
          <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-pink-200/50">
            <div class="text-sm font-medium text-pink-800 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd">
                </path>
              </svg>
              Appearance
            </div>
            <p class="text-pink-700 text-sm leading-relaxed">{{ scene.details.effeminate.appearance|default:'Classic beauty with delicate features' }}</p>
          </div>
          <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-pink-200/50">
            <div class="text-sm font-medium text-pink-800 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z">
                </path>
              </svg>
              Hair Style
            </div>
            <p class="text-pink-700 text-sm leading-relaxed">{{ scene.details.effeminate.hair|default:'Elegantly styled with attention to detail' }}</p>
          </div>

          <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-pink-200/50">
            <div class="text-sm font-medium text-pink-800 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z" clip-rule="evenodd">
                </path>
              </svg>
              Attire
            </div>
            <p class="text-pink-700 text-sm leading-relaxed">{{ scene.details.effeminate.clothing|default:'Beautifully crafted traditional garments' }}</p>
          </div>
        </div>
      </div>

      <!-- Masculine Character -->
      <div
        class="bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50 rounded-2xl p-6 sm:p-8 border border-blue-100">
        <div class="flex items-center mb-4 sm:mb-6">
          <div
            class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-blue-400 to-indigo-400 flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
            <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd">
              </path>
            </svg>
          </div>
          <div class="min-w-0">
            <h3 class="text-lg sm:text-xl font-semibold text-blue-900">Masculine Character</h3>
            <div class="text-sm text-blue-700 font-medium">Age {{ scene.masculine_age }}</div>
          </div>
        </div>

        <div class="space-y-3 sm:space-y-4">
          <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-blue-200/50">
            <div class="text-sm font-medium text-blue-800 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd">
                </path>
              </svg>
              Appearance
            </div>
            <p class="text-blue-700 text-sm leading-relaxed">{{ scene.details.masculine.appearance|default:'Strong presence with commanding features' }}</p>
          </div>

          <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-blue-200/50">
            <div class="text-sm font-medium text-blue-800 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z">
                </path>
              </svg>
              Hair Style
            </div>
            <p class="text-blue-700 text-sm leading-relaxed">{{ scene.details.masculine.hair|default:'Well-groomed with distinguished styling' }}</p>
          </div>

          <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-blue-200/50">
            <div class="text-sm font-medium text-blue-800 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z" clip-rule="evenodd">
                </path>
              </svg>
              Attire
            </div>
            <p class="text-blue-700 text-sm leading-relaxed">{{ scene.details.masculine.clothing|default:'Refined traditional clothing with elegant details' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Atmosphere Section -->
    <div
      class="bg-gradient-to-br from-amber-50 via-orange-50 to-amber-50 rounded-2xl p-6 sm:p-8 border border-amber-100">
      <div class="flex items-center mb-4 sm:mb-6">
        <div
          class="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-r from-amber-400 to-orange-400 flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
          <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
              clip-rule="evenodd"></path>
          </svg>
        </div>
        <div class="min-w-0">
          <h3 class="text-lg sm:text-xl font-semibold text-amber-900">Scene Atmosphere</h3>
          <div class="text-sm text-amber-700">Environmental details and ambiance</div>
        </div>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
        <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-amber-200/50">
          <div class="text-sm font-medium text-amber-800 mb-2 flex items-center">
            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.477.859h4z">
              </path>
            </svg>
            Lighting
          </div>
          <p class="text-amber-700 text-sm leading-relaxed">{{ scene.details.atmosphere.lighting|default:'Warm, ambient lighting creating intimacy' }}</p>
        </div>

        <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-amber-200/50">
          <div class="text-sm font-medium text-amber-800 mb-2 flex items-center">
            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
            </svg>
            Fragrance
          </div>
          <p class="text-amber-700 text-sm leading-relaxed">{{ scene.details.atmosphere.scent|default:'Subtle, romantic fragrances in the air' }}</p>
        </div>

        <div class="bg-white/60 rounded-xl p-3 sm:p-4 border border-amber-200/50 sm:col-span-2 lg:col-span-1">
          <div class="text-sm font-medium text-amber-800 mb-2 flex items-center">
            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-2.21-.896-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 12a5.984 5.984 0 01-.757 2.828 1 1 0 01-1.415-1.414A3.984 3.984 0 0013 12a3.983 3.983 0 00-.172-1.414 1 1 0 010-1.415z"
                clip-rule="evenodd"></path>
            </svg>
            Soundscape
          </div>
          <p class="text-amber-700 text-sm leading-relaxed">{{ scene.details.atmosphere.sound|default:'Gentle, melodious sounds enhancing the mood' }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Scene Description -->
  <section class="px-4 sm:px-8 py-6 sm:py-8">
    <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
      <div class="min-w-0">
        <h2 class="text-xl sm:text-2xl font-light text-gray-900 mb-2">Complete Scene Description</h2>
        <p class="text-gray-600 text-sm sm:text-base">The full narrative prompt for this romantic scene</p>
      </div>

      <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
        <div class="text-sm text-gray-500">
          <span class="font-medium">{{ scene.full_text|length }}</span> characters
        </div>
        <button id="copy-btn"
          class="group inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-xl bg-gradient-to-r from-primary to-primary-dark text-white font-medium hover:from-primary-dark hover:to-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base"
          aria-label="Copy prompt" data-scene-id="{{ scene.id }}">
          <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform" fill="none"
            stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
            </path>
          </svg>
          Copy to Clipboard
        </button>
      </div>
    </div>

    <div class="relative">
      <div id="prompt-text"
        class="bg-gradient-to-br from-gray-50 to-white rounded-2xl p-6 sm:p-8 text-gray-800 leading-relaxed whitespace-pre-line border border-gray-200 shadow-inner font-serif text-sm sm:text-base">
        {{ scene.full_text }}</div>

      <!-- Reading Progress Indicator -->
      <div class="mt-4 flex flex-col sm:flex-row sm:items-center justify-between text-xs text-gray-500 gap-2">
        <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
          <span>📖 Reading time: ~{{ scene.full_text|length|floatformat:0|add:"0"|slice:":1" }} min</span>
          <span>📝 Words: ~{{ scene.full_text|wordcount }}</span>
        </div>
        <div class="flex items-center gap-2">
          <span>Scroll to read</span>
          <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>
    </div>
  </section>

  <!-- Image Gallery Section -->
  <section class="px-4 sm:px-8 py-6 sm:py-8 border-t border-gray-100">
    <div class="flex flex-col gap-4 mb-6 sm:mb-8">
      <div class="min-w-0">
        <h2 class="text-xl sm:text-2xl font-light text-gray-900 mb-2">Scene Images</h2>
        <p class="text-gray-600 text-sm sm:text-base">Visual references and illustrations for this scene</p>
      </div>

      <!-- Mobile-optimized button layout -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-end gap-3">
        <!-- Bulk Actions (hidden by default) -->
        <div id="bulk-actions" class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
          <span id="selected-count" class="text-sm text-gray-600 text-center sm:text-left py-2 sm:py-0">0
            selected</span>
          <div class="flex flex-col sm:flex-row gap-2">
            <button id="bulk-delete-btn"
              class="mobile-btn inline-flex items-center justify-center px-4 py-3 rounded-xl bg-red-600 text-white font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 text-sm touch-target">
              <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1v3M4 7h16">
                </path>
              </svg>
              <span class="truncate">Delete Selected</span>
            </button>
            <button id="select-all-btn"
              class="mobile-btn inline-flex items-center justify-center px-4 py-3 rounded-xl bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-all duration-200 text-sm touch-target">
              <span class="truncate">Select All</span>
            </button>
            <button id="cancel-selection-btn"
              class="mobile-btn inline-flex items-center justify-center px-4 py-3 rounded-xl bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-all duration-200 text-sm touch-target">
              <span class="truncate">Cancel</span>
            </button>
          </div>
        </div>

        <!-- Upload Button -->
        <button id="upload-images-btn"
          class="mobile-btn group inline-flex items-center justify-center px-4 sm:px-6 py-3 rounded-xl bg-gradient-to-r from-green-600 to-green-700 text-white font-medium hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base touch-target">
          <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform flex-shrink-0" fill="none"
            stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          <span class="truncate">Upload Images</span>
        </button>
      </div>
    </div>

    <!-- Drag and Drop Upload Area -->
    <div id="upload-area" class="hidden mb-6 sm:mb-8">
      <div
        class="border-2 border-dashed border-gray-300 rounded-2xl p-6 sm:p-8 lg:p-12 text-center bg-gray-50 hover:bg-gray-100 transition-colors duration-200 touch-target">
        <div class="flex flex-col items-center">
          <svg class="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 text-gray-400 mb-3 sm:mb-4" fill="none"
            stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          <h3 class="text-base sm:text-lg lg:text-xl font-semibold text-gray-700 mb-2">Drop images here or tap to browse
          </h3>
          <p class="text-gray-500 text-sm sm:text-base mb-4 px-2">Support for JPEG, PNG, WebP files up to 10MB each</p>
          <input type="file" id="file-input" multiple accept="image/*" class="hidden">
          <button type="button" id="browse-btn"
            class="mobile-btn inline-flex items-center justify-center px-6 py-3 rounded-xl bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors touch-target font-medium">
            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"></path>
            </svg>
            <span class="truncate">Browse Files</span>
          </button>
        </div>
      </div>

      <!-- Upload Progress -->
      <div id="upload-progress" class="hidden mt-4">
        <div class="bg-white rounded-lg border border-gray-200 p-4">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Uploading images...</span>
            <span id="upload-percentage" class="text-sm text-gray-500">0%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div id="upload-bar" class="bg-green-600 h-2 rounded-full transition-all duration-300" style="width: 0%">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Images Grid -->
    <div id="images-grid"
      class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-2 sm:gap-3 lg:gap-4">
      {% for image in scene.images %}
      <div class="image-item group relative touch-target cursor-pointer" data-image-id="{{ image.id }}"
        onclick="toggleImageSelection(this, event)">
        <!-- Selection Indicator -->
        <div class="selection-indicator absolute top-2 right-2 z-20 opacity-0 transition-opacity duration-200">
          <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
        </div>

        <!-- Image -->
        <div
          class="aspect-square relative overflow-hidden rounded-lg sm:rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border-2 border-transparent">
          <img src="{{ image.thumbnail.url|default:image.image.url }}" alt="Scene image {{ forloop.counter }}"
            class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" loading="lazy">

          <!-- Mobile-optimized overlay with actions -->
          <div
            class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center rounded-lg sm:rounded-xl">
            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-2 sm:space-x-3">
              <button onclick="event.stopPropagation(); openImageModal('{{ image.image.url }}', 'Scene image')"
                class="mobile-btn p-2 sm:p-3 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-200 shadow-lg touch-target"
                aria-label="View image">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </button>
              <button onclick="event.stopPropagation(); showDeleteConfirmation('{{ image.id }}')"
                class="mobile-btn p-2 sm:p-3 bg-red-500 bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-200 shadow-lg touch-target"
                aria-label="Delete image">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                  </path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
      {% empty %}
      <div id="no-images-message" class="col-span-full text-center py-8 sm:py-12">
        <svg class="w-12 h-12 sm:w-16 sm:h-16 text-gray-300 mx-auto mb-3 sm:mb-4" fill="none" stroke="currentColor"
          viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
          </path>
        </svg>
        <h3 class="text-base sm:text-lg font-medium text-gray-700 mb-2">No images yet</h3>
        <p class="text-gray-500 text-sm sm:text-base px-4">Upload some images to bring this scene to life!</p>
      </div>
      {% endfor %}
    </div>
  </section>
</article>

<script>
  // Image upload functionality
  document.addEventListener('DOMContentLoaded', function () {
    const uploadBtn = document.getElementById('upload-images-btn');
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    const browseBtn = document.getElementById('browse-btn');
    const uploadProgress = document.getElementById('upload-progress');
    const uploadBar = document.getElementById('upload-bar');
    const uploadPercentage = document.getElementById('upload-percentage');
    const imagesGrid = document.getElementById('images-grid');
    const noImagesMessage = document.getElementById('no-images-message');

    // Toggle upload area
    uploadBtn.addEventListener('click', function () {
      uploadArea.classList.toggle('hidden');
    });

    // Browse button click
    browseBtn.addEventListener('click', function () {
      fileInput.click();
    });

    // File input change
    fileInput.addEventListener('change', function (e) {
      if (e.target.files.length > 0) {
        uploadFiles(e.target.files);
      }
    });

    // Drag and drop functionality
    const dropArea = uploadArea.querySelector('.border-dashed');

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      dropArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
      dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      dropArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
      dropArea.classList.add('border-green-500', 'bg-green-50');
    }

    function unhighlight(e) {
      dropArea.classList.remove('border-green-500', 'bg-green-50');
    }

    dropArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
      const dt = e.dataTransfer;
      const files = dt.files;
      uploadFiles(files);
    }

    // Upload files function
    function uploadFiles(files) {
      const formData = new FormData();

      for (let i = 0; i < files.length; i++) {
        formData.append('images', files[i]);
      }

      // Show progress
      uploadProgress.classList.remove('hidden');
      uploadBar.style.width = '0%';
      uploadPercentage.textContent = '0%';

      // Simulate progress (you can replace with actual progress tracking)
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        uploadBar.style.width = progress + '%';
        uploadPercentage.textContent = Math.round(progress) + '%';
      }, 200);

      // Upload to server
      fetch(`/scene/{{ scene.id }}/upload-images/`, {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRFToken': getCookie('csrftoken')
        }
      })
        .then(response => response.json())
        .then(data => {
          clearInterval(progressInterval);
          uploadBar.style.width = '100%';
          uploadPercentage.textContent = '100%';

          setTimeout(() => {
            uploadProgress.classList.add('hidden');
            uploadArea.classList.add('hidden');

            if (data.success && data.uploaded_images.length > 0) {
              // Remove no images message
              if (noImagesMessage) {
                noImagesMessage.remove();
              }

              // Add new images to grid
              data.uploaded_images.forEach(image => {
                addImageToGrid(image);
              });

              showNotification(`Successfully uploaded ${data.uploaded_images.length} image(s)`, 'success');
            }

            if (data.errors && data.errors.length > 0) {
              showNotification(`Some errors occurred: ${data.errors.join(', ')}`, 'error');
            }
          }, 1000);
        })
        .catch(error => {
          clearInterval(progressInterval);
          uploadProgress.classList.add('hidden');
          showNotification('Upload failed. Please try again.', 'error');
          console.error('Upload error:', error);
        });

      // Reset file input
      fileInput.value = '';
    }

    // Add image to grid
    function addImageToGrid(image) {
      const imageHtml = `
            <div class="image-item group relative touch-target cursor-pointer" data-image-id="${image.id}" onclick="toggleImageSelection(this, event)">
                <!-- Selection Indicator -->
                <div class="selection-indicator absolute top-2 right-2 z-20 opacity-0 transition-opacity duration-200">
                  <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>

                <div class="aspect-square relative overflow-hidden rounded-lg sm:rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border-2 border-transparent">
                    <img src="${image.thumbnail_url}" 
                         alt="Scene image"
                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                         loading="lazy">
                    
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center rounded-lg sm:rounded-xl">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-2 sm:space-x-3">
                            <button onclick="event.stopPropagation(); openImageModal('${image.url}', 'Scene image')"
                                    class="mobile-btn p-2 sm:p-3 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-200 shadow-lg touch-target"
                                    aria-label="View image">
                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                            <button onclick="event.stopPropagation(); showDeleteConfirmation('${image.id}')"
                                    class="mobile-btn p-2 sm:p-3 bg-red-500 bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all duration-200 shadow-lg touch-target"
                                    aria-label="Delete image">
                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

      imagesGrid.insertAdjacentHTML('beforeend', imageHtml);
    }
  });

  // Image modal functions
  function openImageModal(imageUrl, caption) {
    const modal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    const modalCaption = document.getElementById('modal-caption');

    modalImage.src = imageUrl;
    modalCaption.textContent = caption || '';
    modal.classList.remove('hidden');

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  function closeImageModal() {
    const modal = document.getElementById('image-modal');
    modal.classList.add('hidden');

    // Restore body scroll
    document.body.style.overflow = '';
  }

  // Update bulk actions based on selected images
  function updateBulkActions() {
    const selectedImages = document.querySelectorAll('.image-item.selected');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    if (selectedImages.length > 0) {
      bulkActions.classList.remove('hidden');
      bulkActions.classList.add('flex');
      selectedCount.textContent = `${selectedImages.length} selected`;
    } else {
      bulkActions.classList.add('hidden');
      bulkActions.classList.remove('flex');
    }
  }

  // Show delete confirmation modal (similar to scene delete)
  function showDeleteConfirmation(imageId) {
    showDeleteModal([imageId], false);
  }

  function showBulkDeleteConfirmation(imageIds) {
    showDeleteModal(imageIds, true);
  }

  function showDeleteModal(imageIds, isBulk) {
    const count = imageIds.length;
    const title = isBulk ? `Delete ${count} Images` : 'Delete Image';
    const message = isBulk ?
      `Are you absolutely sure you want to delete ${count} selected images? This action cannot be undone.` :
      'Are you absolutely sure you want to delete this image? This action cannot be undone.';

    // Create confirmation modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">${title}</h3>
          <p class="text-gray-600 mb-6">${message}</p>

          <div class="flex items-center justify-center space-x-4">
            <button class="cancel-delete px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors">
              Cancel
            </button>
            <button class="confirm-delete px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              Yes, Delete
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Handle modal interactions
    const cancelBtn = modal.querySelector('.cancel-delete');
    const confirmBtn = modal.querySelector('.confirm-delete');

    const closeModal = () => {
      document.body.removeChild(modal);
    };

    cancelBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModal();
    });

    confirmBtn.addEventListener('click', () => {
      closeModal();
      showFinalConfirmation(imageIds, isBulk);
    });
  }

  function showFinalConfirmation(imageIds, isBulk) {
    const count = imageIds.length;
    const title = 'Final Warning';
    const message = isBulk ?
      `Last chance! This will permanently delete ${count} images. This action cannot be undone. Continue?` :
      'Last chance! This will permanently delete this image. This action cannot be undone. Continue?';

    // Create final confirmation modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">${title}</h3>
          <p class="text-gray-600 mb-6">${message}</p>

          <div class="flex items-center justify-center space-x-4">
            <button class="cancel-final-delete px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors">
              Cancel
            </button>
            <button class="confirm-final-delete px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              Delete Permanently
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Handle final modal interactions
    const cancelBtn = modal.querySelector('.cancel-final-delete');
    const confirmBtn = modal.querySelector('.confirm-final-delete');

    const closeModal = () => {
      document.body.removeChild(modal);
    };

    cancelBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModal();
    });

    confirmBtn.addEventListener('click', () => {
      // Show loading state
      confirmBtn.innerHTML = `
        <svg class="w-4 h-4 mr-2 animate-spin inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Deleting...
      `;
      confirmBtn.disabled = true;

      // Perform deletion
      if (isBulk) {
        bulkDeleteImages(imageIds);
      } else {
        deleteImage(imageIds[0]);
      }

      closeModal();
    });
  }

  // Single image delete function
  function deleteImage(imageId) {
    fetch(`/scene/{{ scene.id }}/image/${imageId}/delete/`, {
      method: 'DELETE',
      headers: {
        'X-CSRFToken': getCookie('csrftoken')
      }
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          removeImageFromGrid(imageId);
          showNotification('Image deleted successfully', 'success');
        } else {
          showNotification('Failed to delete image', 'error');
        }
      })
      .catch(error => {
        showNotification('Error deleting image', 'error');
        console.error('Delete error:', error);
      });
  }

  // Bulk delete function
  function bulkDeleteImages(imageIds) {
    fetch(`/scene/{{ scene.id }}/bulk-delete-images/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCookie('csrftoken')
      },
      body: JSON.stringify({ image_ids: imageIds })
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Remove images from grid
          imageIds.forEach(id => removeImageFromGrid(id));

          // Clear selection
          document.getElementById('cancel-selection-btn').click();

          showNotification(`Successfully deleted ${data.deleted_count} image(s)`, 'success');
        } else {
          showNotification('Failed to delete images', 'error');
        }
      })
      .catch(error => {
        showNotification('Error deleting images', 'error');
        console.error('Bulk delete error:', error);
      });
  }

  // Helper function to remove image from grid
  function removeImageFromGrid(imageId) {
    const imageItem = document.querySelector(`[data-image-id="${imageId}"]`);
    if (imageItem) {
      imageItem.remove();
    }

    // Check if no images left
    const remainingImages = document.querySelectorAll('.image-item');
    if (remainingImages.length === 0) {
      const noImagesHtml = `
        <div id="no-images-message" class="col-span-full text-center py-8 sm:py-12">
          <svg class="w-12 h-12 sm:w-16 sm:h-16 text-gray-300 mx-auto mb-3 sm:mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <h3 class="text-base sm:text-lg font-medium text-gray-700 mb-2">No images yet</h3>
          <p class="text-gray-500 text-sm sm:text-base px-4">Upload some images to bring this scene to life!</p>
        </div>
      `;
      document.getElementById('images-grid').innerHTML = noImagesHtml;
    }
  }



  // Utility functions
  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${type === 'success' ? 'bg-green-500 text-white' :
      type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
      }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }

  // Close modal on escape key
  document.addEventListener('keydown', function (e) {
    if (e.key === 'Escape') {
      closeImageModal();
    }
  });

  // Close modal on background click
  document.getElementById('image-modal').addEventListener('click', function (e) {
    if (e.target === this) {
      closeImageModal();
    }
  });

  // Toggle image selection
  function toggleImageSelection(imageItem, event) {
    event.preventDefault();

    const imageId = imageItem.dataset.imageId;
    const isSelected = imageItem.classList.contains('selected');
    const selectionIndicator = imageItem.querySelector('.selection-indicator');
    const imageContainer = imageItem.querySelector('.aspect-square');

    if (isSelected) {
      // Deselect
      imageItem.classList.remove('selected');
      selectionIndicator.classList.remove('opacity-100');
      selectionIndicator.classList.add('opacity-0');
      imageContainer.classList.remove('border-blue-500');
      imageContainer.classList.add('border-transparent');
    } else {
      // Select
      imageItem.classList.add('selected');
      selectionIndicator.classList.remove('opacity-0');
      selectionIndicator.classList.add('opacity-100');
      imageContainer.classList.remove('border-transparent');
      imageContainer.classList.add('border-blue-500');
    }

    updateBulkActions();
  }

  // Update bulk actions based on selected images
  function updateBulkActions() {
    const selectedImages = document.querySelectorAll('.image-item.selected');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    if (selectedImages.length > 0) {
      bulkActions.classList.remove('hidden');
      bulkActions.classList.add('flex');
      selectedCount.textContent = `${selectedImages.length} selected`;
    } else {
      bulkActions.classList.add('hidden');
      bulkActions.classList.remove('flex');
    }
  }

  // Show bulk delete confirmation
  function showBulkDeleteConfirmation(imageIds) {
    showDeleteModal(imageIds, true);
  }

  // Show single delete confirmation
  function showDeleteConfirmation(imageId) {
    showDeleteModal([imageId], false);
  }

  // Show delete confirmation modal
  function showDeleteModal(imageIds, isBulk) {
    const count = imageIds.length;
    const title = isBulk ? `Delete ${count} Images` : 'Delete Image';
    const message = isBulk ?
      `Are you absolutely sure you want to delete ${count} selected images? This action cannot be undone.` :
      'Are you absolutely sure you want to delete this image? This action cannot be undone.';

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">${title}</h3>
          <p class="text-gray-600 mb-6">${message}</p>
          <div class="flex items-center justify-center space-x-4">
            <button class="cancel-delete px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors">Cancel</button>
            <button class="confirm-delete px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">Yes, Delete</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    const cancelBtn = modal.querySelector('.cancel-delete');
    const confirmBtn = modal.querySelector('.confirm-delete');
    const closeModal = () => document.body.removeChild(modal);

    cancelBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => { if (e.target === modal) closeModal(); });

    confirmBtn.addEventListener('click', () => {
      confirmBtn.innerHTML = 'Deleting...';
      confirmBtn.disabled = true;
      if (isBulk) { bulkDeleteImages(imageIds); } else { deleteImage(imageIds[0]); }
      closeModal();
    });
  }

  // Delete single image
  function deleteImage(imageId) {
    fetch(`/scene/{{ scene.id }}/image/${imageId}/delete/`, {
      method: 'DELETE',
      headers: { 'X-CSRFToken': getCookie('csrftoken') }
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          removeImageFromGrid(imageId);
          showNotification('Image deleted successfully', 'success');
        } else {
          showNotification('Failed to delete image', 'error');
        }
      })
      .catch(() => showNotification('Error deleting image', 'error'));
  }

  // Bulk delete images
  function bulkDeleteImages(imageIds) {
    fetch(`/scene/{{ scene.id }}/bulk-delete-images/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'X-CSRFToken': getCookie('csrftoken') },
      body: JSON.stringify({ image_ids: imageIds })
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          imageIds.forEach(id => removeImageFromGrid(id));
          document.getElementById('cancel-selection-btn').click();
          showNotification(`Successfully deleted ${data.deleted_count} image(s)`, 'success');
        } else {
          showNotification('Failed to delete images', 'error');
        }
      })
      .catch(() => showNotification('Error deleting images', 'error'));
  }

  // Remove image from grid
  function removeImageFromGrid(imageId) {
    const imageItem = document.querySelector(`[data-image-id="${imageId}"]`);
    if (imageItem) imageItem.remove();

    if (document.querySelectorAll('.image-item').length === 0) {
      document.getElementById('images-grid').innerHTML = `
        <div id="no-images-message" class="col-span-full text-center py-8 sm:py-12">
          <svg class="w-12 h-12 sm:w-16 sm:h-16 text-gray-300 mx-auto mb-3 sm:mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <h3 class="text-base sm:text-lg font-medium text-gray-700 mb-2">No images yet</h3>
          <p class="text-gray-500 text-sm sm:text-base px-4">Upload some images to bring this scene to life!</p>
        </div>
      `;
    }
  }

  // Get CSRF token
  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  // Show notification
  function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${type === 'success' ? 'bg-green-500 text-white' :
      type === 'error' ? 'bg-red-500 text-white' : 'bg-blue-500 text-white'
      }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => notification.classList.remove('translate-x-full'), 100);
    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => { if (document.body.contains(notification)) document.body.removeChild(notification); }, 300);
    }, 3000);
  }

  // Initialize bulk action event listeners when DOM is loaded
  document.addEventListener('DOMContentLoaded', function () {
    console.log('DOM loaded, setting up button listeners...');

    // Bulk delete button
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    console.log('Bulk delete button:', bulkDeleteBtn);
    if (bulkDeleteBtn) {
      bulkDeleteBtn.onclick = function () {
        console.log('Bulk delete clicked');
        const selectedImages = document.querySelectorAll('.image-item.selected');
        console.log('Selected images:', selectedImages.length);
        const imageIds = Array.from(selectedImages).map(item => item.dataset.imageId);
        if (imageIds.length > 0) {
          alert('Would delete images: ' + imageIds.join(', '));
          showBulkDeleteConfirmation(imageIds);
        } else {
          alert('No images selected');
        }
      };
    }

    // Select all button
    const selectAllBtn = document.getElementById('select-all-btn');
    console.log('Select all button:', selectAllBtn);
    if (selectAllBtn) {
      selectAllBtn.onclick = function () {
        console.log('Select all clicked');
        const allImages = document.querySelectorAll('.image-item');
        console.log('All images found:', allImages.length);
        const allSelected = Array.from(allImages).every(item => item.classList.contains('selected'));
        console.log('All selected?', allSelected);

        allImages.forEach(item => {
          if (allSelected) {
            // Deselect all
            if (item.classList.contains('selected')) {
              toggleImageSelection(item, { preventDefault: () => { } });
            }
          } else {
            // Select all
            if (!item.classList.contains('selected')) {
              toggleImageSelection(item, { preventDefault: () => { } });
            }
          }
        });

        // Update button text
        this.textContent = allSelected ? 'Select All' : 'Deselect All';
      };
    }

    // Cancel selection button
    const cancelBtn = document.getElementById('cancel-selection-btn');
    console.log('Cancel button:', cancelBtn);
    if (cancelBtn) {
      cancelBtn.onclick = function () {
        console.log('Cancel clicked');
        const selectedImages = document.querySelectorAll('.image-item.selected');
        selectedImages.forEach(item => {
          toggleImageSelection(item, { preventDefault: () => { } });
        });

        // Reset select all button text
        const selectAllBtn = document.getElementById('select-all-btn');
        if (selectAllBtn) {
          selectAllBtn.textContent = 'Select All';
        }
      };
    }
  });
</script>
</article>

<!-- Mobile-Optimized Image Modal -->
<div id="image-modal"
  class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center p-2 sm:p-4">
  <div class="relative w-full h-full max-w-6xl max-h-full flex items-center justify-center">
    <!-- Close Button -->
    <button onclick="closeImageModal()"
      class="mobile-btn absolute top-4 right-4 z-20 p-3 bg-black bg-opacity-60 rounded-full text-white hover:bg-opacity-80 transition-all duration-200 touch-target"
      aria-label="Close image">
      <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>

    <!-- Image Container -->
    <div class="relative w-full h-full flex items-center justify-center">
      <img id="modal-image" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
        style="max-height: calc(100vh - 8rem);">
    </div>

    <!-- Caption -->
    <div id="modal-caption"
      class="absolute bottom-4 left-4 right-4 text-white text-center bg-black bg-opacity-60 rounded-lg p-3 text-sm sm:text-base backdrop-blur-sm">
    </div>
  </div>
</div>

{% endblock %}