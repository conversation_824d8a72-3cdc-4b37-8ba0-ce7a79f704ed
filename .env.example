# Django Settings
SECRET_KEY=your-secret-key-here-change-this-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
# For SQLite (default)
DATABASE_URL=sqlite:///db.sqlite3

# For PostgreSQL (production recommended)
# DATABASE_URL=postgresql://username:password@localhost:5432/scenes_db

# For MySQL
# DATABASE_URL=mysql://username:password@localhost:3306/scenes_db

# Redis Configuration (optional, for caching)
REDIS_URL=redis://localhost:6379/0

# Email Configuration (optional)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# AWS S3 Configuration (optional, for static files)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1

# Security Settings (production)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# Analytics Settings
ANALYTICS_CACHE_TIMEOUT=3600

# Logging Level
LOG_LEVEL=INFO