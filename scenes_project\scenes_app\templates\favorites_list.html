{% extends 'base.html' %}
{% block content %}
<!-- Hero Section -->
<div class="relative mb-12">
  <div class="absolute inset-0 bg-gradient-to-r from-red-500/5 to-pink-500/5 rounded-2xl"></div>
  <div class="relative px-8 py-12 text-center">
    <h1 class="text-4xl font-light text-gray-900 mb-3 tracking-tight">
      My <span class="font-semibold text-red-500">Favorite</span> Scenes
    </h1>
    <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
      Your personally curated collection of romantic scenes that captured your heart
    </p>
    
    <!-- Enhanced Controls -->
    <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
      <div class="flex items-center gap-3">
        <a href="/" 
           class="group inline-flex items-center px-6 py-3 rounded-xl bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 font-medium hover:from-gray-200 hover:to-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-all duration-200 shadow-sm hover:shadow-md">
          <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to All Scenes
        </a>
        
        {% if total_favorites > 0 %}
          <a href="{% url 'random_scene' %}" 
             class="group inline-flex items-center px-6 py-3 rounded-xl bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl">
            <svg class="w-5 h-5 mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Random Scene
          </a>
        {% endif %}
      </div>
      
      <div class="flex items-center px-4 py-2 rounded-full bg-red-50 border border-red-200">
        <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
        <span class="text-sm font-medium text-red-700">{{ total_favorites }} Favorite{{ total_favorites|pluralize }}</span>
      </div>
    </div>
  </div>
</div>

{% if total_favorites > 0 %}
  <!-- Stats Bar -->
  <div class="mb-8 bg-white rounded-xl border border-gray-200 shadow-sm">
    <div class="px-6 py-4 flex items-center justify-between">
      <div class="flex items-center space-x-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-red-500">{{ total_favorites }}</div>
          <div class="text-xs text-gray-500 uppercase tracking-wide">Favorite Scenes</div>
        </div>
        <div class="w-px h-8 bg-gray-200"></div>
        <div class="text-center">
          <div class="text-2xl font-bold text-primary" data-stat="current-page">{{ page_obj.number }}</div>
          <div class="text-xs text-gray-500 uppercase tracking-wide">Current Page</div>
        </div>
        <div class="w-px h-8 bg-gray-200"></div>
        <div class="text-center">
          <div class="text-2xl font-bold text-accent" data-stat="total-pages">{{ page_obj.paginator.num_pages }}</div>
          <div class="text-xs text-gray-500 uppercase tracking-wide">Total Pages</div>
        </div>
      </div>
      
      <div class="hidden sm:flex items-center text-sm text-gray-500">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }}
      </div>
    </div>
  </div>

  {% include 'partials/_scene_cards.html' with favorites_only=True %}
{% else %}
  <!-- Empty State -->
  <div class="text-center py-16">
    <div class="w-32 h-32 mx-auto mb-6 text-gray-300">
      <svg fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
      </svg>
    </div>
    <h3 class="text-2xl font-light text-gray-900 mb-4">No Favorite Scenes Yet</h3>
    <p class="text-gray-600 mb-8 max-w-md mx-auto">
      Start exploring scenes and click the heart icon to add them to your favorites collection.
    </p>
    <a href="/" 
       class="inline-flex items-center px-8 py-4 rounded-xl bg-gradient-to-r from-primary to-primary-dark text-white font-medium hover:from-primary-dark hover:to-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
      Explore Scenes
    </a>
  </div>
{% endif %}
{% endblock %}