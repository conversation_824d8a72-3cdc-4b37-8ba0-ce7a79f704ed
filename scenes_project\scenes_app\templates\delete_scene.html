{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="max-w-2xl mx-auto">
  <!-- Header -->
  <div class="mb-8 text-center">
    <div class="w-20 h-20 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
      <svg class="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
      </svg>
    </div>
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Delete Scene</h1>
    <p class="text-gray-600">This action cannot be undone</p>
  </div>

  <!-- Scene Info Card -->
  <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden mb-8">
    <div class="px-8 py-6 bg-gradient-to-r from-red-50 to-pink-50 border-b border-gray-100">
      <h2 class="text-xl font-semibold text-gray-900 mb-2">Scene to be deleted</h2>
      <p class="text-gray-600">Review the scene details before confirming deletion</p>
    </div>
    
    <div class="px-8 py-8">
      <div class="space-y-4">
        <div class="flex items-center justify-between py-3 border-b border-gray-100">
          <span class="text-sm font-medium text-gray-600">Scene ID</span>
          <span class="text-sm text-gray-900 font-mono">#{{ scene.id }}</span>
        </div>
        
        <div class="flex items-center justify-between py-3 border-b border-gray-100">
          <span class="text-sm font-medium text-gray-600">Title</span>
          <span class="text-sm text-gray-900 font-semibold">{{ scene.title }}</span>
        </div>
        
        <div class="flex items-center justify-between py-3 border-b border-gray-100">
          <span class="text-sm font-medium text-gray-600">Country</span>
          <span class="text-sm text-gray-900">{{ scene.country }}</span>
        </div>
        
        <div class="flex items-center justify-between py-3 border-b border-gray-100">
          <span class="text-sm font-medium text-gray-600">Setting</span>
          <span class="text-sm text-gray-900">{{ scene.setting }}</span>
        </div>
        
        <div class="flex items-center justify-between py-3 border-b border-gray-100">
          <span class="text-sm font-medium text-gray-600">Emotion</span>
          <span class="text-sm text-gray-900">{{ scene.emotion }}</span>
        </div>
        
        <div class="flex items-center justify-between py-3 border-b border-gray-100">
          <span class="text-sm font-medium text-gray-600">Character Ages</span>
          <span class="text-sm text-gray-900">{{ scene.effeminate_age }} / {{ scene.masculine_age }}</span>
        </div>
        
        <div class="flex items-center justify-between py-3 border-b border-gray-100">
          <span class="text-sm font-medium text-gray-600">Images</span>
          <span class="text-sm text-gray-900">{{ image_count }} image(s)</span>
        </div>
        
        <div class="flex items-center justify-between py-3">
          <span class="text-sm font-medium text-gray-600">Favorites</span>
          <span class="text-sm text-gray-900">{{ scene.favorite_count }} user(s)</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Warning Message -->
  <div class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
    <div class="flex items-start">
      <svg class="w-6 h-6 text-red-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
      </svg>
      <div>
        <h3 class="text-lg font-semibold text-red-800 mb-2">Warning: Permanent Deletion</h3>
        <ul class="text-sm text-red-700 space-y-1">
          <li>• This scene will be permanently removed from the database</li>
          <li>• All {{ image_count }} associated image(s) will be deleted</li>
          <li>• The scene's image folder will be completely removed</li>
          <li>• All associated favorites will be deleted</li>
          <li>• This action cannot be undone</li>
          <li>• Make sure you really want to delete this scene</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="flex items-center justify-center space-x-4">
    <a href="{% url 'scene_detail' scene.id %}" 
       class="inline-flex items-center px-8 py-3 rounded-xl bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-all duration-200">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
      Cancel
    </a>
    

    
    <form method="post" class="inline-block" id="delete-form">
      {% csrf_token %}
      <button type="button" id="confirm-delete"
              class="inline-flex items-center px-8 py-3 rounded-xl bg-gradient-to-r from-red-600 to-red-700 text-white font-medium hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1v3M4 7h16"></path>
        </svg>
        Delete Permanently
      </button>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const deleteBtn = document.getElementById('confirm-delete');
  const deleteForm = document.getElementById('delete-form');

  deleteBtn.addEventListener('click', function() {
    showDeleteConfirmationModal();
  });

  function showDeleteConfirmationModal() {
    // Create first confirmation modal
    const modal1 = document.createElement('div');
    modal1.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal1.innerHTML = `
      <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">Delete Scene</h3>
          <p class="text-gray-600 mb-6">Are you absolutely sure you want to delete "<strong>{{ scene.title }}</strong>" and its {{ image_count }} image(s)? This action cannot be undone.</p>

          <div class="flex items-center justify-center space-x-4">
            <button class="cancel-delete px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors">
              Cancel
            </button>
            <button class="confirm-first-delete px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              Yes, Delete
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal1);

    // Handle first modal interactions
    const cancelBtn1 = modal1.querySelector('.cancel-delete');
    const confirmBtn1 = modal1.querySelector('.confirm-first-delete');

    const closeModal1 = () => {
      document.body.removeChild(modal1);
    };

    cancelBtn1.addEventListener('click', closeModal1);
    modal1.addEventListener('click', (e) => {
      if (e.target === modal1) closeModal1();
    });

    confirmBtn1.addEventListener('click', () => {
      closeModal1();
      showSecondConfirmationModal();
    });
  }

  function showSecondConfirmationModal() {
    // Create second confirmation modal (final warning)
    const modal2 = document.createElement('div');
    modal2.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal2.innerHTML = `
      <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">Final Warning</h3>
          <p class="text-gray-600 mb-6">Last chance! This will permanently delete "<strong>{{ scene.title }}</strong>" and its {{ image_count }} image(s). This action cannot be undone. Continue?</p>

          <div class="flex items-center justify-center space-x-4">
            <button class="cancel-final-delete px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors">
              Cancel
            </button>
            <button class="confirm-final-delete px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              Delete Permanently
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal2);

    // Handle second modal interactions
    const cancelBtn2 = modal2.querySelector('.cancel-final-delete');
    const confirmBtn2 = modal2.querySelector('.confirm-final-delete');

    const closeModal2 = () => {
      document.body.removeChild(modal2);
    };

    cancelBtn2.addEventListener('click', closeModal2);
    modal2.addEventListener('click', (e) => {
      if (e.target === modal2) closeModal2();
    });

    confirmBtn2.addEventListener('click', () => {
      // Show loading state
      confirmBtn2.innerHTML = `
        <svg class="w-4 h-4 mr-2 animate-spin inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Deleting...
      `;
      confirmBtn2.disabled = true;

      // Update the original delete button to show loading state
      deleteBtn.disabled = true;
      deleteBtn.innerHTML = '<svg class="w-5 h-5 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Deleting...';

      // Submit the form
      deleteForm.submit();
    });
  }
});
</script>
{% endblock %}