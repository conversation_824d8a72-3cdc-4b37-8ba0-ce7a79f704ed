# Generated by Django 5.2.4 on 2025-08-12 20:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('scenes_app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SearchQuery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.CharField(max_length=255)),
                ('results_count', models.PositiveIntegerField(default=0)),
                ('session_key', models.CharField(blank=True, max_length=40)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['query', 'created_at'], name='scenes_app__query_0bed3c_idx'), models.Index(fields=['session_key', 'created_at'], name='scenes_app__session_4db42b_idx')],
            },
        ),
        migrations.CreateModel(
            name='SearchSuggestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('term', models.CharField(db_index=True, max_length=255)),
                ('suggestion_type', models.CharField(choices=[('title', 'Title'), ('country', 'Country'), ('setting', 'Setting'), ('emotion', 'Emotion'), ('content', 'Content'), ('character', 'Character')], db_index=True, max_length=20)),
                ('frequency', models.PositiveIntegerField(default=1)),
                ('last_used', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-frequency', '-last_used'],
                'indexes': [models.Index(fields=['term', 'suggestion_type'], name='scenes_app__term_517c5f_idx'), models.Index(fields=['frequency', 'last_used'], name='scenes_app__frequen_100f3f_idx')],
                'unique_together': {('term', 'suggestion_type')},
            },
        ),
    ]
