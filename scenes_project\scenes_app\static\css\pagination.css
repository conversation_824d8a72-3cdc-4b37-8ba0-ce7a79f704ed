/* Enhanced Pagination Styles */

.pagination-container {
  margin-top: 2rem;
}

@media (min-width: 640px) {
  .pagination-container {
    margin-top: 3rem;
  }
}

/* Pagination buttons */
.pagination-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
  min-width: 44px;
  padding: 0.5rem 0.75rem;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  user-select: none;
}

.pagination-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pagination-btn:active {
  transform: translateY(0);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Primary pagination button (Next) */
.pagination-btn.btn-primary {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

.pagination-btn.btn-primary:hover {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Secondary pagination buttons */
.pagination-btn.btn-secondary {
  background: #f8fafc;
  color: #475569;
  border-color: #e2e8f0;
}

.pagination-btn.btn-secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #334155;
}

/* Current page button */
.pagination-btn.btn-current {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
  cursor: default;
}

.pagination-btn.btn-current:hover {
  transform: none;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
}

/* Disabled state */
.pagination-btn.btn-disabled {
  background: #f8fafc;
  color: #94a3b8;
  border-color: #e2e8f0;
  cursor: not-allowed;
}

.pagination-btn.btn-disabled:hover {
  transform: none;
  background: #f8fafc;
  box-shadow: none;
}

/* Page size selector */
#page-size-selector {
  min-height: 44px;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease-in-out;
}

#page-size-selector:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Jump to page input */
#jump-to-page {
  min-height: 44px;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  text-align: center;
  background: white;
  transition: all 0.2s ease-in-out;
}

#jump-to-page:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Jump to page button */
#jump-to-page-btn {
  min-height: 44px;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

#jump-to-page-btn:hover {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
}

#jump-to-page-btn:active {
  transform: translateY(0);
}

/* Progress bar */
.pagination-progress {
  width: 100%;
  height: 0.5rem;
  background: #e2e8f0;
  border-radius: 0.25rem;
  overflow: hidden;
}

.pagination-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 0.25rem;
  transition: width 0.5s ease-out;
}

/* Keyboard shortcuts */
.keyboard-shortcuts {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.keyboard-shortcut {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #64748b;
}

.keyboard-shortcut kbd {
  padding: 0.25rem 0.5rem;
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 0.25rem;
  font-family: ui-monospace, SFMono-Regular, monospace;
  font-size: 0.75rem;
}

/* Loading states */
.pagination-loading .pagination-btn {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.pagination-loading .pagination-btn:hover {
  transform: none;
  box-shadow: none;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .pagination-container {
    margin-top: 1.5rem;
  }
  
  .pagination-btn {
    min-height: 40px;
    min-width: 40px;
    padding: 0.5rem;
    font-size: 0.8125rem;
  }
  
  .pagination-btn span {
    display: none;
  }
  
  .pagination-btn svg {
    width: 1rem;
    height: 1rem;
  }
  
  #page-size-selector,
  #jump-to-page,
  #jump-to-page-btn {
    min-height: 40px;
    padding: 0.5rem;
    font-size: 0.8125rem;
  }
  
  .keyboard-shortcuts {
    display: none;
  }
}

/* Tablet responsive adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
  .pagination-btn {
    min-height: 42px;
    min-width: 42px;
  }
}

/* Animation for page transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pagination-content-enter {
  animation: fadeInUp 0.3s ease-out;
}

/* Focus styles for accessibility */
.pagination-btn:focus-visible {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

#page-size-selector:focus-visible,
#jump-to-page:focus-visible,
#jump-to-page-btn:focus-visible {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .pagination-btn {
    border: 2px solid currentColor;
  }
  
  .pagination-btn.btn-current {
    background: #000;
    color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .pagination-btn,
  #jump-to-page-btn,
  .pagination-progress-bar {
    transition: none;
  }
  
  .pagination-btn:hover {
    transform: none;
  }
  
  .pagination-content-enter {
    animation: none;
  }
}

/* Print styles */
@media print {
  .pagination-container {
    display: none;
  }
}
