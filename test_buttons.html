<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Functionality Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Button Functionality Test</h1>
        
        <!-- Bulk Actions (initially hidden) -->
        <div id="bulk-actions" class="hidden flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3 mb-6">
            <span id="selected-count" class="text-sm text-gray-600 text-center sm:text-left py-2 sm:py-0">0 selected</span>
            <div class="flex flex-col sm:flex-row gap-2">
                <button id="bulk-delete-btn" class="px-4 py-3 rounded-xl bg-red-600 text-white font-medium hover:bg-red-700">
                    Delete Selected
                </button>
                <button id="select-all-btn" class="px-4 py-3 rounded-xl bg-gray-100 text-gray-700 font-medium hover:bg-gray-200">
                    Select All
                </button>
                <button id="cancel-selection-btn" class="px-4 py-3 rounded-xl bg-gray-100 text-gray-700 font-medium hover:bg-gray-200">
                    Cancel
                </button>
            </div>
        </div>

        <!-- Test Images Grid -->
        <div id="images-grid" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            <!-- Test Image 1 -->
            <div class="image-item group relative cursor-pointer" data-image-id="1" onclick="toggleImageSelection(this, event)">
                <div class="selection-indicator absolute top-2 right-2 z-20 opacity-0 transition-opacity duration-200">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="aspect-square relative overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border-2 border-transparent bg-gray-300 flex items-center justify-center">
                    <span class="text-gray-600">Image 1</span>
                </div>
            </div>

            <!-- Test Image 2 -->
            <div class="image-item group relative cursor-pointer" data-image-id="2" onclick="toggleImageSelection(this, event)">
                <div class="selection-indicator absolute top-2 right-2 z-20 opacity-0 transition-opacity duration-200">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="aspect-square relative overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border-2 border-transparent bg-gray-300 flex items-center justify-center">
                    <span class="text-gray-600">Image 2</span>
                </div>
            </div>

            <!-- Test Image 3 -->
            <div class="image-item group relative cursor-pointer" data-image-id="3" onclick="toggleImageSelection(this, event)">
                <div class="selection-indicator absolute top-2 right-2 z-20 opacity-0 transition-opacity duration-200">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="aspect-square relative overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border-2 border-transparent bg-gray-300 flex items-center justify-center">
                    <span class="text-gray-600">Image 3</span>
                </div>
            </div>

            <!-- Test Image 4 -->
            <div class="image-item group relative cursor-pointer" data-image-id="4" onclick="toggleImageSelection(this, event)">
                <div class="selection-indicator absolute top-2 right-2 z-20 opacity-0 transition-opacity duration-200">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="aspect-square relative overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border-2 border-transparent bg-gray-300 flex items-center justify-center">
                    <span class="text-gray-600">Image 4</span>
                </div>
            </div>
        </div>

        <div class="mt-8 p-4 bg-white rounded-lg">
            <h2 class="text-xl font-bold mb-4">Test Instructions:</h2>
            <ol class="list-decimal list-inside space-y-2">
                <li>Click on images to select them (they should get a blue border and checkmark)</li>
                <li>When images are selected, the bulk actions bar should appear</li>
                <li>Test "Select All" button - should select all images</li>
                <li>Test "Cancel" button - should deselect all images</li>
                <li>Test "Delete Selected" button - should show confirmation modal</li>
            </ol>
        </div>
    </div>

    <script>
        // Toggle image selection
        function toggleImageSelection(imageItem, event) {
            event.preventDefault();
            
            const imageId = imageItem.dataset.imageId;
            const isSelected = imageItem.classList.contains('selected');
            const selectionIndicator = imageItem.querySelector('.selection-indicator');
            const imageContainer = imageItem.querySelector('.aspect-square');
            
            if (isSelected) {
                // Deselect
                imageItem.classList.remove('selected');
                selectionIndicator.classList.remove('opacity-100');
                selectionIndicator.classList.add('opacity-0');
                imageContainer.classList.remove('border-blue-500');
                imageContainer.classList.add('border-transparent');
            } else {
                // Select
                imageItem.classList.add('selected');
                selectionIndicator.classList.remove('opacity-0');
                selectionIndicator.classList.add('opacity-100');
                imageContainer.classList.remove('border-transparent');
                imageContainer.classList.add('border-blue-500');
            }
            
            updateBulkActions();
        }

        // Update bulk actions based on selected images
        function updateBulkActions() {
            const selectedImages = document.querySelectorAll('.image-item.selected');
            const bulkActions = document.getElementById('bulk-actions');
            const selectedCount = document.getElementById('selected-count');
            
            if (selectedImages.length > 0) {
                bulkActions.classList.remove('hidden');
                bulkActions.classList.add('flex');
                selectedCount.textContent = `${selectedImages.length} selected`;
            } else {
                bulkActions.classList.add('hidden');
                bulkActions.classList.remove('flex');
            }
        }

        function showBulkDeleteConfirmation(imageIds) {
            alert(`Would delete images: ${imageIds.join(', ')}`);
        }

        // Initialize bulk action event listeners when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Bulk delete button
            const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
            if (bulkDeleteBtn) {
                bulkDeleteBtn.addEventListener('click', function() {
                    const selectedImages = document.querySelectorAll('.image-item.selected');
                    const imageIds = Array.from(selectedImages).map(item => item.dataset.imageId);
                    if (imageIds.length > 0) {
                        showBulkDeleteConfirmation(imageIds);
                    } else {
                        alert('No images selected');
                    }
                });
            }

            // Select all button
            const selectAllBtn = document.getElementById('select-all-btn');
            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', function() {
                    const allImages = document.querySelectorAll('.image-item');
                    const allSelected = Array.from(allImages).every(item => item.classList.contains('selected'));
                    
                    allImages.forEach(item => {
                        if (allSelected) {
                            // Deselect all
                            if (item.classList.contains('selected')) {
                                toggleImageSelection(item, { preventDefault: () => {} });
                            }
                        } else {
                            // Select all
                            if (!item.classList.contains('selected')) {
                                toggleImageSelection(item, { preventDefault: () => {} });
                            }
                        }
                    });
                    
                    // Update button text
                    this.textContent = allSelected ? 'Select All' : 'Deselect All';
                });
            }

            // Cancel selection button
            const cancelBtn = document.getElementById('cancel-selection-btn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', function() {
                    const selectedImages = document.querySelectorAll('.image-item.selected');
                    selectedImages.forEach(item => {
                        toggleImageSelection(item, { preventDefault: () => {} });
                    });
                    
                    // Reset select all button text
                    const selectAllBtn = document.getElementById('select-all-btn');
                    if (selectAllBtn) {
                        selectAllBtn.textContent = 'Select All';
                    }
                });
            }
        });
    </script>
</body>
</html>