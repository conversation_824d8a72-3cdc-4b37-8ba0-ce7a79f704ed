# Generated by Django 5.2.4 on 2025-08-13 11:02

import django.db.models.deletion
import scenes_project.scenes_app.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('scenes_app', '0003_auto_20250813_0157'),
    ]

    operations = [
        migrations.CreateModel(
            name='SceneImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to=scenes_project.scenes_app.models.scene_image_upload_path)),
                ('thumbnail', models.ImageField(blank=True, null=True, upload_to=scenes_project.scenes_app.models.scene_image_upload_path)),
                ('caption', models.CharField(blank=True, max_length=255)),
                ('order', models.PositiveIntegerField(default=0)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('file_size', models.PositiveIntegerField(default=0)),
                ('width', models.PositiveIntegerField(default=0)),
                ('height', models.PositiveIntegerField(default=0)),
                ('scene', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scene_images', to='scenes_app.scene')),
            ],
            options={
                'ordering': ['order', 'uploaded_at'],
                'indexes': [models.Index(fields=['scene', 'order'], name='scenes_app__scene_i_938d90_idx')],
            },
        ),
    ]
